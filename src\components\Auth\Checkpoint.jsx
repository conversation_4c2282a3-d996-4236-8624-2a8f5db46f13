import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { useAuth } from './AuthContext';
import { LOGIN_MUTATION } from '../../../api/mutations';
import './Checkpoint.css';

export default function Checkpoint() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { login } = useAuth();

  const [loginMutation] = useMutation(LOGIN_MUTATION, {
    onCompleted: (data) => {
      console.log('Login successful:', data);
      login(data.login.agent, data.login.token);
      navigate('/');
      toast.success('Agent Authenticated');
    },
    onError: (error) => {
      console.error('Login error:', error);
      setError(error.message || 'Login failed. Please check your credentials.');
      toast.error('Failed to Authenticate');
    }
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      console.log('Attempting login with:', { username });
      await loginMutation({
        variables: { username, password }
      });
    } catch (err) {
      console.error('Login mutation error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="ckpnt-container">
      <div className="ckpnt-box">
        <h2>Login</h2>
        {error && <div className="ckpnt-error-message">{error}</div>}
        <form onSubmit={handleSubmit} autoComplete="off">
          <div className="ckpnt-form-group">
            <label htmlFor="username">Username</label>
            <input
              type="text"
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              disabled={isLoading}
              autoComplete="off"
            />
          </div>
          <div className="ckpnt-form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={isLoading}
              autoComplete="new-password"
            />
          </div>
          <button type="submit" className="ckpnt-button" disabled={isLoading}>
            {isLoading ? 'Logging in...' : 'Login'}
          </button>
        </form>
      </div>
    </div>
  );
} 