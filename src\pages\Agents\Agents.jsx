import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { GET_AGENTS } from '../../../api/queries';
import { DELETE_AGENT } from '../../../api/mutations';
import CreateAgentModal from './CreateAgent';
import '../../../css/shared.css';

export default function Agents() {
  const { loading, error, data, refetch } = useQuery(GET_AGENTS);
  const [copiedId, setCopiedId] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [deleteAgentMutation] = useMutation(DELETE_AGENT, {
    onCompleted: () => {
      refetch();
      toast.success('Agent deleted successfully!');
    },
    onError: (deleteError) => {
      console.error('Error deleting agent:', deleteError);
      toast.error(`Error deleting agent: ${deleteError.message}`);
    }
  });

  const handleDeleteAgent = (agentId, agentName) => {
    if (window.confirm(`Are you sure you want to delete agent "${agentName}" (ID: ${agentId})? This action cannot be undone.`)) {
      deleteAgentMutation({ variables: { hiveId: agentId } });
    }
  };

  const copyToClipboard = (id) => {
    navigator.clipboard.writeText(id);
    setCopiedId(id);
    setTimeout(() => setCopiedId(null), 2000);
  };

  if (loading) return <div style={{ padding: '1rem' }}>Loading agents...</div>;
  if (error) return <div style={{ padding: '1rem' }}>Error: {error.message}</div>;

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <div style={{ padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>Agents</h1>
        <button onClick={openModal} className="button-primary">
          Create Agent
        </button>
      </div>

      <CreateAgentModal isOpen={isModalOpen} onClose={closeModal} />

      <div style={{ marginTop: '2rem' }}>
        {data?.agents?.map((agent) => (
          <div 
            key={agent.hiveId}
            style={{
              padding: '1rem',
              marginBottom: '1rem',
              borderRadius: '4px',
              backgroundColor: '#222',
              position: 'relative'
            }}
          >
            <button 
              onClick={() => handleDeleteAgent(agent.hiveId, agent.username)}
              className="button-delete"
              style={{ position: 'absolute', top: '12px', right: '16px' }}
              title="Delete Agent"
            >
              Delete
            </button>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginRight: '60px' }}>
              <h3>{agent.username}</h3>
              <span 
                style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                onClick={() => copyToClipboard(agent.hiveId)}
                title="Click to copy Hive ID"
              >
                {agent.hiveId}
                {copiedId === agent.hiveId && ' ✓'}
              </span>
            </div>
            <hr></hr>
            <div style={{ display: 'flex', gap: '1rem' }}>
              <div style={{ 
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <strong>Role:</strong> <p style={{color: '#909090'}}>{agent.role}</p>
                </div>
              </div>
              {agent.backingPerson && agent.backingPerson.length > 0 && (
                <div style={{
                  flex: 1,
                  padding: '1rem',
                  backgroundColor: '#2a2a2a',
                  borderRadius: '4px'
                }}>
                  <div>
                    <strong>Backing Person:</strong>
                    {agent.backingPerson.map(person => (
                      <div key={person.hiveId} style={{color: '#909090'}}>
                        <p style={{margin: 0}}>
                          {person.firstName} {person.lastName}
                        </p>
                        <p 
                          style={{fontSize: '0.8em', margin: 0, cursor: 'pointer'}} 
                          onClick={() => copyToClipboard(person.hiveId)} 
                          title="Copy person Hive ID"
                        >
                          Hive ID: {person.hiveId}
                          {copiedId === person.hiveId && ' ✓'}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              {agent.handlesInformant && agent.handlesInformant.length > 0 && (
                <div style={{
                  flex: 1,
                  padding: '1rem',
                  backgroundColor: '#2a2a2a',
                  borderRadius: '4px'
                }}>
                  <div>
                    <strong>Handles Informants:</strong>
                    {agent.handlesInformant.length > 0 ? (
                      <ul>
                        {agent.handlesInformant.map(informant => (
                          <li style={{marginLeft: '18px', color: '#ffda94'}} key={informant.hiveId}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <span>{informant.codeName}</span>
                              <span 
                                style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                                onClick={() => copyToClipboard(informant.hiveId)}
                                title="Click to copy Hive ID"
                              >
                                {informant.hiveId}{copiedId === informant.hiveId && ' ✓'}
                              </span>
                            </div>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p style={{color: '#909090'}}>No informants</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 