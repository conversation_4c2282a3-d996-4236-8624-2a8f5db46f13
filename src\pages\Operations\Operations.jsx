import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { GET_OPERATIONS } from '../../../api/queries';
import { 
  DELETE_OPERATION, 
  REMOVE_TARGET_FROM_OPERATION, 
  REMOVE_SCOPED_CASE_FROM_OPERATION,
  REMOVE_LEAD_AGENT_FROM_OPERATION 
} from '../../../api/mutations';
import CreateOperationModal from './CreateOperationModal';
import AddTargetModal from './AddTargetModal';
import AddCaseScopeModal from './AddCaseScopeModal';
import AssignLeadAgentModal from './AssignLeadAgentModal';
import '../../../css/shared.css';

export default function Operations() {
  const { loading, error, data, refetch } = useQuery(GET_OPERATIONS);
  const [copiedId, setCopiedId] = useState(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isAddTargetModalOpen, setIsAddTargetModalOpen] = useState(false);
  const [isAddCaseScopeModalOpen, setIsAddCaseScopeModalOpen] = useState(false);
  const [isAssignLeadAgentModalOpen, setIsAssignLeadAgentModalOpen] = useState(false);
  const [selectedOperation, setSelectedOperation] = useState(null);

  const [deleteOperationMutation] = useMutation(DELETE_OPERATION, {
    onCompleted: () => {
      refetch();
      toast.success('Operation deleted successfully!');
    },
    onError: (deleteError) => {
      console.error('Error deleting operation:', deleteError);
      toast.error(`Error deleting operation: ${deleteError.message}`);
    }
  });

  const [removeTargetMutation] = useMutation(REMOVE_TARGET_FROM_OPERATION, {
    onCompleted: () => {
      refetch();
      toast.success('Target removed successfully!');
    },
    onError: (error) => {
      console.error('Error removing target:', error);
      toast.error(`Error removing target: ${error.message}`);
    }
  });

  const [removeCaseScopeMutation] = useMutation(REMOVE_SCOPED_CASE_FROM_OPERATION, {
    onCompleted: () => {
      refetch();
      toast.success('Case removed from scope successfully!');
    },
    onError: (error) => {
      console.error('Error removing case scope:', error);
      toast.error(`Error removing case scope: ${error.message}`);
    }
  });

  const [removeLeadAgentMutation] = useMutation(REMOVE_LEAD_AGENT_FROM_OPERATION, {
    onCompleted: () => {
      refetch();
      toast.success('Lead agent removed successfully!');
    },
    onError: (error) => {
      console.error('Error removing lead agent:', error);
      toast.error(`Error removing lead agent: ${error.message}`);
    }
  });

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    setCopiedId(text);
    setTimeout(() => setCopiedId(null), 2000);
  };

  const openCreateModal = () => setIsCreateModalOpen(true);
  const closeCreateModal = () => setIsCreateModalOpen(false);

  const openAddTargetModal = (operation) => {
    setSelectedOperation(operation);
    setIsAddTargetModalOpen(true);
  };
  const closeAddTargetModal = () => {
    setIsAddTargetModalOpen(false);
    setSelectedOperation(null);
  };

  const openAddCaseScopeModal = (operation) => {
    setSelectedOperation(operation);
    setIsAddCaseScopeModalOpen(true);
  };
  const closeAddCaseScopeModal = () => {
    setIsAddCaseScopeModalOpen(false);
    setSelectedOperation(null);
  };

  const openAssignLeadAgentModal = (operation) => {
    setSelectedOperation(operation);
    setIsAssignLeadAgentModalOpen(true);
  };
  const closeAssignLeadAgentModal = () => {
    setIsAssignLeadAgentModalOpen(false);
    setSelectedOperation(null);
  };

  const handleDeleteOperation = (hiveId) => {
    if (window.confirm('Are you sure you want to delete this operation?')) {
      deleteOperationMutation({ variables: { hiveId } });
    }
  };

  const handleRemoveTarget = (targetHiveId, operationHiveId) => {
    if (window.confirm('Are you sure you want to remove this target?')) {
      removeTargetMutation({ 
        variables: { 
          targetHiveId, 
          operationHiveId 
        } 
      });
    }
  };

  const handleRemoveCaseScope = (caseHiveId, operationHiveId) => {
    if (window.confirm('Are you sure you want to remove this case from scope?')) {
      removeCaseScopeMutation({ 
        variables: { 
          caseHiveId, 
          operationHiveId 
        } 
      });
    }
  };

  const handleRemoveLeadAgent = (operationHiveId) => {
    if (window.confirm('Are you sure you want to remove the lead agent?')) {
      removeLeadAgentMutation({ 
        variables: { 
          operationHiveId 
        } 
      });
    }
  };

  const formatTargetName = (target) => {
    switch (target.__typename) {
      case 'Person':
        return `${target.firstName} ${target.lastName}`;
      case 'Organization':
        return target.name;
      case 'Vehicle':
        return `${target.make} ${target.model} (${target.color})`;
      default:
        return 'Unknown Target';
    }
  };

  if (loading) return <p>Loading operations...</p>;
  if (error) return <p>Error loading operations: {error.message}</p>;

  return (
    <div style={{ padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>Operations</h1>
        <button onClick={openCreateModal} className="button-primary">
          Create Operation
        </button>
      </div>

      <CreateOperationModal isOpen={isCreateModalOpen} onClose={closeCreateModal} />
      {selectedOperation && (
        <>
          <AddTargetModal
            isOpen={isAddTargetModalOpen}
            onClose={closeAddTargetModal}
            operation={selectedOperation}
            onRefetch={refetch}
          />
          <AddCaseScopeModal
            isOpen={isAddCaseScopeModalOpen}
            onClose={closeAddCaseScopeModal}
            operation={selectedOperation}
            onRefetch={refetch}
          />
          <AssignLeadAgentModal
            isOpen={isAssignLeadAgentModalOpen}
            onClose={closeAssignLeadAgentModal}
            operation={selectedOperation}
            onRefetch={refetch}
          />
        </>
      )}

      <div style={{ marginTop: '2rem' }}>
        {data.operations.map((operation) => (
          <div 
            key={operation.hiveId}
            style={{
              padding: '1rem',
              marginBottom: '1rem',
              borderRadius: '4px',
              backgroundColor: '#222',
              position: 'relative'
            }}
          >
            <div style={{ position: 'absolute', top: '12px', right: '16px', display: 'flex', gap: '10px' }}>
              <button
                onClick={() => openAssignLeadAgentModal(operation)}
                className="button-utility"
                title={operation.leadAgent && operation.leadAgent.length > 0 ? 'Change Lead Agent' : 'Assign Lead Agent'}
              >
                {operation.leadAgent && operation.leadAgent.length > 0 ? 'Change Lead' : 'Assign Lead'}
              </button>
              <button
                onClick={() => handleDeleteOperation(operation.hiveId)}
                className="button-delete"
                title="Delete Operation"
              >
                Delete
              </button>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginRight: '150px' }}>
              <h3 style={{ margin: 0, color: '#fff' }}>
                {operation.title}
              </h3>
              <span
                style={{
                  fontSize: '0.8em',
                  color: copiedId === operation.hiveId ? '#4CAF50' : '#909090',
                  cursor: 'pointer',
                  userSelect: 'none'
                }}
                onClick={() => copyToClipboard(operation.hiveId)}
                title="Click to copy Hive ID"
              >
                {operation.hiveId}
                {copiedId === operation.hiveId && ' ✓'}
              </span>
            </div>
            <hr />
            <p style={{ margin: '0.25rem 0', color: '#ccc' }}>
              <strong>Type:</strong> {operation.type}
            </p>
            <p style={{ margin: '0.25rem 0', color: '#ccc' }}>
              <strong>Created:</strong> {new Date(operation.creationDate).toLocaleDateString()}
            </p>

            {/* Targets Section */}
            <div style={{ marginTop: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                <h4 style={{ margin: 0, color: '#fff' }}>Targets:</h4>
                <button 
                  onClick={() => openAddTargetModal(operation)}
                  className="button-add-small"
                  title="Add Target"
                >
                  +
                </button>
              </div>
              {operation.targets && operation.targets.length > 0 ? (
                <ul style={{ margin: '0.5rem 0', paddingLeft: '1rem' }}>
                  {operation.targets.map((target) => (
                    <li key={target.hiveId} style={{ color: '#ccc', marginBottom: '0.25rem' }}>
                      <span>{formatTargetName(target)} (ID: {target.hiveId})</span>
                      <button 
                        onClick={() => handleRemoveTarget(target.hiveId, operation.hiveId)}
                        className="button-delete"
                        style={{ marginLeft: '0.5rem', fontSize: '10px', padding: '2px 6px' }}
                      >
                        Remove
                      </button>
                    </li>
                  ))}
                </ul>
              ) : (
                <p style={{ color: '#888', margin: '0.5rem 0', fontStyle: 'italic' }}>No targets assigned</p>
              )}
            </div>

            {/* Lead Agent Section */}
            <div style={{ marginTop: '1rem' }}>
              <h4 style={{ margin: '0 0 0.5rem 0', color: '#fff' }}>Lead Agent:</h4>
              {operation.leadAgent && operation.leadAgent.length > 0 ? (
                <div style={{ color: '#ccc', marginBottom: '0.25rem' }}>
                  <span>
                    {operation.leadAgent[0].username}
                    ({operation.leadAgent[0].backingPerson[0]?.firstName} {operation.leadAgent[0].backingPerson[0]?.lastName})
                  </span>
                  <button
                    onClick={() => handleRemoveLeadAgent(operation.hiveId)}
                    className="button-delete"
                    style={{ marginLeft: '0.5rem', fontSize: '10px', padding: '2px 6px' }}
                  >
                    Remove
                  </button>
                </div>
              ) : (
                <p style={{ color: '#888', margin: '0.5rem 0', fontStyle: 'italic' }}>No lead agent assigned</p>
              )}
            </div>

            {/* Case Scope Section */}
            <div style={{ marginTop: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                <h4 style={{ margin: 0, color: '#fff' }}>Case Scope:</h4>
                <button 
                  onClick={() => openAddCaseScopeModal(operation)}
                  className="button-add-small"
                  title="Add Case to Scope"
                >
                  +
                </button>
              </div>
              {operation.scopedToCase && operation.scopedToCase.length > 0 ? (
                <ul style={{ margin: '0.5rem 0', paddingLeft: '1rem' }}>
                  {operation.scopedToCase.map((caseItem) => (
                    <li key={caseItem.hiveId} style={{ color: '#ccc', marginBottom: '0.25rem' }}>
                      <span>{caseItem.title} (Status: {caseItem.status})</span>
                      <button 
                        onClick={() => handleRemoveCaseScope(caseItem.hiveId, operation.hiveId)}
                        className="button-delete"
                        style={{ marginLeft: '0.5rem', fontSize: '10px', padding: '2px 6px' }}
                      >
                        Remove
                      </button>
                    </li>
                  ))}
                </ul>
              ) : (
                <p style={{ color: '#888', margin: '0.5rem 0', fontStyle: 'italic' }}>No cases in scope</p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
