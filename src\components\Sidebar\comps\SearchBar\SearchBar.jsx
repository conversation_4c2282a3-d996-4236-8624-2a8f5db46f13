import React, { useRef, useState, useEffect } from 'react';
import { FiPlus, FiSearch } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import styles from '../../../../../css/sidebar.module.css';

export default function SearchBar({ isCollapsed, handleCollapse }) {
  const searchInputRef = useRef(null);
  const [searchResult, setSearchResult] = useState(null);
  const [hasSearched, setHasSearched] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();

  // When the sidebar is collapsed, clear any preview cards
  useEffect(() => {
    if (isCollapsed) {
      setHasSearched(false);
      setSearchResult(null);
    }
  }, [isCollapsed]);

  const handleSearch = async (id) => {
    if (!id) return;
    
    try {
      setHasSearched(true);
      const result = await api.fetchApi(
        `${api.getUrl('SEARCH')}/${id}`
      );
      setSearchResult(result);
    } catch (err) {
      console.error('Search failed:', err);
      setSearchResult(null);
    }
  };

  const focusSearchInput = () => {
    setTimeout(() => {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      } else {
        console.error('Search input ref is null');
      }
    }, 80);
  };

  const handleSearchClick = async () => {
    if (isCollapsed) {
      handleCollapse();
      focusSearchInput();
    } else {
      const trimmed = searchTerm.trim();
      if (!trimmed) return;
      setHasSearched(true);
      const result = await lookupEntity(trimmed);
      setSearchResult(result);
    }
  };

  // Handle Enter key press on search input
  const handleKeyDown = async (e) => {
    if (e.key === 'Enter') {
      const trimmed = searchTerm.trim();
      if (!trimmed) return;
      setHasSearched(true);
      const result = await lookupEntity(trimmed);
      setSearchResult(result);
    }
  };

  // When clicking the preview card, navigate to the profile and clear the search.
  const handleProfileRedirect = (entity) => {
    navigate(`/spider/${entity.hiveId}`);
    setHasSearched(false);
    setSearchResult(null);
    setSearchTerm("");
  };

  return (
    <div>
      <div className={`${styles['search-bar']} ${isCollapsed ? styles.collapsed : ''}`}>
        {!isCollapsed && (
          <div className={styles['search-input-wrapper']}>
            <input
              type="text"
              ref={searchInputRef}
              placeholder="Find by ID..."
              className={styles['search-input']}
              value={searchTerm}
              onChange={(e) => {
                const value = e.target.value;
                setSearchTerm(value);
                if (value.trim() === "") {
                  setHasSearched(false);
                  setSearchResult(null);
                }
              }}
              onKeyDown={handleKeyDown}
            />
          </div>
        )}
        <button className={styles['search-button']} onClick={handleSearchClick}>
          <FiSearch size={22} />
        </button>
        <button className={styles['add-button']}>
          <FiPlus size={26} /> 
        </button>
      </div>
      {hasSearched && (
        <div className={styles['search-result']} style={{ marginTop: '10px' }}>
          <div style={{ marginBottom: '4px', fontSize: '14px', color: '#aaa' }}>
            {searchResult ? "Entity Found" : "No Matching Entity Found"}
          </div>
          {searchResult ? (
            <div onClick={() => handleProfileRedirect(searchResult)}>
              {renderPreviewCard(searchResult)}
            </div>
          ) : (
            <InvalidEntity id={searchTerm} />
          )}
        </div>
      )}
    </div>
  );
}
