import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { GET_CASES } from '../../../api/queries';
import { DELETE_CASE } from '../../../api/mutations';
import CreateCaseModal from './CreateCase';
import ManageSuspectsModal from './ManageSuspectsModal';
import ManageVictimsModal from './ManageVictimsModal';
import ManageWitnessesModal from './ManageWitnessesModal';
import '../../../css/shared.css';

export default function Cases() {
  const { loading, error, data, refetch } = useQuery(GET_CASES);
  const [copiedId, setCopiedId] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAddSuspectModalOpen, setIsAddSuspectModalOpen] = useState(false);
  const [isAddVictimModalOpen, setIsAddVictimModalOpen] = useState(false);
  const [isAddWitnessModalOpen, setIsAddWitnessModalOpen] = useState(false);
  const [selectedCase, setSelectedCase] = useState(null);

  // Helper function to get fresh case data from the current query results
  const getFreshCaseData = (caseHiveId) => {
    return data?.cases?.find(c => c.hiveId === caseHiveId) || null;
  };

  const [deleteCaseMutation] = useMutation(DELETE_CASE, {
    onCompleted: () => {
      refetch();
      toast.success('Case deleted successfully!');
    },
    onError: (deleteError) => {
      console.error('Error deleting case:', deleteError);
      toast.error(`Error deleting case: ${deleteError.message}`);
    }
  });

  const handleDeleteCase = (caseId, caseTitle) => {
    if (window.confirm(`Are you sure you want to delete case "${caseTitle}" (ID: ${caseId})? This action cannot be undone.`)) {
      deleteCaseMutation({ variables: { hiveId: caseId } });
    }
  };

  const copyToClipboard = (id) => {
    navigator.clipboard.writeText(id);
    setCopiedId(id);
    setTimeout(() => setCopiedId(null), 2000);
  };

  if (loading) return <div style={{ padding: '1rem' }}>Loading cases...</div>;
  if (error) return <div style={{ padding: '1rem' }}>Error: {error.message}</div>;

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const openAddSuspectModal = (caseItem) => {
    setSelectedCase(caseItem);
    setIsAddSuspectModalOpen(true);
  };
  const closeAddSuspectModal = () => {
    setIsAddSuspectModalOpen(false);
    setSelectedCase(null);
  };

  const openAddVictimModal = (caseItem) => {
    setSelectedCase(caseItem);
    setIsAddVictimModalOpen(true);
  };
  const closeAddVictimModal = () => {
    setIsAddVictimModalOpen(false);
    setSelectedCase(null);
  };

  const openAddWitnessModal = (caseItem) => {
    setSelectedCase(caseItem);
    setIsAddWitnessModalOpen(true);
  };
  const closeAddWitnessModal = () => {
    setIsAddWitnessModalOpen(false);
    setSelectedCase(null);
  };

  return (
    <div style={{ padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>Cases</h1>
        <button onClick={openModal} className="button-primary">
          Create Case
        </button>
      </div>

      <CreateCaseModal isOpen={isModalOpen} onClose={closeModal} />
      {selectedCase && (
        <>
          <ManageSuspectsModal
            isOpen={isAddSuspectModalOpen}
            onClose={closeAddSuspectModal}
            caseData={getFreshCaseData(selectedCase.hiveId)}
            onRefetch={refetch}
          />
          <ManageVictimsModal
            isOpen={isAddVictimModalOpen}
            onClose={closeAddVictimModal}
            caseData={getFreshCaseData(selectedCase.hiveId)}
            onRefetch={refetch}
          />
          <ManageWitnessesModal
            isOpen={isAddWitnessModalOpen}
            onClose={closeAddWitnessModal}
            caseData={getFreshCaseData(selectedCase.hiveId)}
            onRefetch={refetch}
          />
        </>
      )}

      <div style={{ marginTop: '2rem' }}>
        {data.cases.map((case_) => (
          <div 
            key={case_.hiveId}
            style={{
              padding: '1rem',
              marginBottom: '1rem',
              borderRadius: '4px',
              backgroundColor: '#222',
              position: 'relative'
            }}
          >
            <button 
              onClick={() => handleDeleteCase(case_.hiveId, case_.title)}
              className="button-delete"
              style={{ position: 'absolute', top: '12px', right: '16px' }}
              title="Delete Case"
            >
              Delete
            </button>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginRight: '60px' }}>
              <h3>{case_.title}</h3>
              <span 
                style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                onClick={() => copyToClipboard(case_.hiveId)}
                title="Click to copy Hive ID"
              >
                {case_.hiveId}
                {copiedId === case_.hiveId && ' ✓'}
              </span>
            </div>
            <hr></hr>
            <div style={{ display: 'flex', gap: '1rem' }}>
              <div style={{ 
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <strong>Created:</strong> <p style={{color: '#909090'}}>
                    {case_.creationDate ? 
                      new Date(case_.creationDate).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: '2-digit' })
                      : 'Missing'}
                  </p>
                </div>
                  <div style={{margin: '6px 0' }}></div>
                  <div>
                  <strong>Status:</strong> <p style={{color: '#909090', textTransform: 'capitalize'}}>
                    {case_.status ? case_.status.toLowerCase() : 'Unknown'}
                  </p>
                </div>
              </div>
              <div style={{ 
                flex: 1,
                padding: '1rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px'
              }}>
                <div>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <strong>Suspects:</strong>
                    <button
                      onClick={() => openAddSuspectModal(case_)}
                      className="button-utility"
                    >
                      Manage
                    </button>
                  </div>
                  {case_.suspects.length > 0 ? (
                    <ul>
                      {case_.suspects.map(suspect => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={suspect.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {`${suspect.firstName} ${suspect.lastName}`}
                            <span 
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(suspect.hiveId)}
                              title="Click to copy Hive ID"
                            >
                              {suspect.hiveId}{copiedId === suspect.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>No suspects</p>
                  )}
                </div>
                <div>
                  <div style={{margin: '6px 0' }}></div>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <strong>Victims:</strong>
                    <button
                      onClick={() => openAddVictimModal(case_)}
                      className="button-utility"
                    >
                      Manage
                    </button>
                  </div>
                  {case_.victims.length > 0 ? (
                    <ul>
                      {case_.victims.map(victim => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={victim.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {`${victim.firstName} ${victim.lastName}`}
                            <span 
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(victim.hiveId)}
                              title="Click to copy Hive ID"
                            >
                              {victim.hiveId}{copiedId === victim.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>No victims</p>
                  )}
                </div>
                <div>
                  <div style={{margin: '6px 0' }}></div>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <strong>Witnesses:</strong>
                    <button
                      onClick={() => openAddWitnessModal(case_)}
                      className="button-add-small"
                      title="Add Witness"
                    >
                      Manage
                    </button>
                  </div>
                  {case_.witnesses.length > 0 ? (
                    <ul>
                      {case_.witnesses.map(witness => (
                        <li style={{marginLeft: '18px', color: '#ffda94'}} key={witness.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            {`${witness.firstName} ${witness.lastName}`}
                            <span 
                              style={{fontSize: '0.8em',color: '#909090',cursor: 'pointer',userSelect: 'none'}}
                              onClick={() => copyToClipboard(witness.hiveId)}
                              title="Click to copy Hive ID"
                            >
                              {witness.hiveId}{copiedId === witness.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{color: '#909090'}}>No witnesses</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 